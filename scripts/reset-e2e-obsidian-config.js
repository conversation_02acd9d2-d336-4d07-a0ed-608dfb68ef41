#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const USER_DATA_DIR = path.join(process.cwd(), 'e2e/test_obsidian_data');
const VAULT_PATH = path.join(process.cwd(), 'tests/vault/Test');
const OBSIDIAN_CONFIG_PATH = path.join(USER_DATA_DIR, 'obsidian.json');

function generateVaultId() {
    return crypto.randomBytes(8).toString('hex');
}

function resetObsidianConfig() {
    console.log('🔄 Resetting Obsidian e2e configuration...');

    // Ensure the user data directory exists
    if (!fs.existsSync(USER_DATA_DIR)) {
        fs.mkdirSync(USER_DATA_DIR, { recursive: true });
        console.log('📁 Created user data directory');
    }

    // Ensure the vault directory and .obsidian config exist
    const vaultObsidianDir = path.join(VAULT_PATH, '.obsidian');
    if (!fs.existsSync(vaultObsidianDir)) {
        fs.mkdirSync(vaultObsidianDir, { recursive: true });
        console.log('📁 Created vault .obsidian directory');
    }

    // Create proper vault configuration files
    const vaultAppConfig = {
        legacyEditor: false,
        livePreview: true,
        showLineNumber: false,
        spellcheck: false,
        useMarkdownLinks: false,
        newFileLocation: "folder",
        newFileFolderPath: "articles"
    };

    const vaultWorkspaceConfig = {
        main: {
            id: "main-workspace",
            type: "split",
            children: [
                {
                    id: "main-editor",
                    type: "leaf",
                    state: {
                        type: "markdown",
                        state: {
                            file: "Welcome.md",
                            mode: "source"
                        }
                    }
                }
            ]
        },
        left: {
            id: "left-sidebar",
            type: "split",
            children: [],
            collapsed: true
        },
        right: {
            id: "right-sidebar",
            type: "split",
            children: [],
            collapsed: true
        },
        active: "main-editor",
        lastOpenFiles: ["Welcome.md"]
    };

    const communityPluginsConfig = ["ghost-sync"];

    // Write vault configuration files
    fs.writeFileSync(path.join(vaultObsidianDir, 'app.json'), JSON.stringify(vaultAppConfig, null, 2));
    fs.writeFileSync(path.join(vaultObsidianDir, 'workspace.json'), JSON.stringify(vaultWorkspaceConfig, null, 2));
    fs.writeFileSync(path.join(vaultObsidianDir, 'community-plugins.json'), JSON.stringify(communityPluginsConfig, null, 2));

    console.log('✅ Created vault configuration files');

    // Generate a new vault ID
    const vaultId = generateVaultId();

    // Create the obsidian.json configuration
    const config = {
        vaults: {
            [vaultId]: {
                path: VAULT_PATH,
                ts: Date.now(),
                open: true
            }
        },
        insider: true
    };

    // Write the configuration
    fs.writeFileSync(OBSIDIAN_CONFIG_PATH, JSON.stringify(config, null, 2));

    console.log('✅ Created new obsidian.json configuration');
    console.log(`📍 Vault ID: ${vaultId}`);
    console.log(`📂 Vault Path: ${VAULT_PATH}`);

    // Clean up any corrupted cache files that might interfere
    const filesToClean = [
        'Local State',
        'Preferences',
        'First Run'
    ];

    filesToClean.forEach(file => {
        const filePath = path.join(USER_DATA_DIR, file);
        if (fs.existsSync(filePath)) {
            try {
                fs.unlinkSync(filePath);
                console.log(`🧹 Cleaned up ${file}`);
            } catch (error) {
                console.log(`⚠️  Could not clean ${file}: ${error.message}`);
            }
        }
    });

    // Clean up cache directories
    const cacheDirs = [
        'Cache',
        'Code Cache',
        'GPUCache',
        'DawnGraphiteCache',
        'DawnWebGPUCache'
    ];

    cacheDirs.forEach(dir => {
        const dirPath = path.join(USER_DATA_DIR, dir);
        if (fs.existsSync(dirPath)) {
            try {
                fs.rmSync(dirPath, { recursive: true, force: true });
                console.log(`🧹 Cleaned up ${dir} directory`);
            } catch (error) {
                console.log(`⚠️  Could not clean ${dir}: ${error.message}`);
            }
        }
    });

    console.log('🎉 Obsidian e2e configuration reset complete!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Run: npm run test:e2e:obsidian');
    console.log('2. Obsidian should now open the test vault automatically');
    console.log('3. Run your e2e tests in another terminal');
}

if (require.main === module) {
    resetObsidianConfig();
}

module.exports = { resetObsidianConfig };
